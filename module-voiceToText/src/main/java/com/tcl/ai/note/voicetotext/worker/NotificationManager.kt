package com.tcl.ai.note.voicetotext.worker

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import androidx.core.app.NotificationCompat

private lateinit var mNotificationManager: NotificationManager
const val CHANNEL_ID = "RecordWorkManager"
const val CHANNEL_NAME = "RecordWorkManager"

fun notifyNotification(
    context: Context, notificationId: Int, block: NotificationCompat.Builder.() -> Unit
) {
    val notification = createNotification(context, block)
    val manager = getNotificationManager(context)
    manager.notify(notificationId, notification)
}

fun notifyNotification(context: Context, notificationId: Int, notification: Notification) {
    val manager = getNotificationManager(context)
    manager.notify(notificationId, notification)
}

fun cancelNotification(context: Context, notificationId: Int) {
    val manager = getNotificationManager(context)
    manager.cancel(notificationId)
}
fun cancelAllNotifications(context: Context) {
    val manager = getNotificationManager(context)
    manager.cancelAll()
}

fun createNotification(
    context: Context, block: NotificationCompat.Builder.() -> Unit
): Notification {
    val builder = NotificationCompat.Builder(context, CHANNEL_ID)
    block.invoke(builder)
    createNotificationChannel(context)
    return builder.build()
}

private fun createNotificationChannel(context: Context): NotificationChannel {
    val notificationManager = getNotificationManager(context)
    val channel =
        NotificationChannel(CHANNEL_ID, CHANNEL_NAME, NotificationManager.IMPORTANCE_LOW)
    notificationManager.createNotificationChannel(channel)
    return channel
}

private fun getNotificationManager(context: Context): NotificationManager {
    if (!::mNotificationManager.isInitialized) {
        mNotificationManager =
            context.getSystemService(NotificationManager::class.java)
    }
    return mNotificationManager
}

