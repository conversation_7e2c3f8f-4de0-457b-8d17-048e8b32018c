package com.tcl.ai.note.voicetotext.view.widget

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ClipboardManager
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.focused
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.stateDescription
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.fdp
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.voicetotext.BuildConfig
import com.tcl.ai.note.voicetotext.util.formatAudioTime
import com.tcl.ai.note.voicetotext.data.AudioTransferEntity
import com.tcl.ai.note.voicetotext.data.audioErrorState
import com.tcl.ai.note.voicetotext.data.getAudioTestSuccessState
import com.tcl.ai.note.voicetotext.states.AudioResult
import com.tcl.ai.note.voicetotext.states.AudioStreamingMsg
import com.tcl.ai.note.voicetotext.states.AudioStreamingStatus
import com.tcl.ai.note.widget.accessibilityDescription
import com.tcl.ai.note.widget.components.AICommonResultPanelColumn
import com.tcl.ai.note.widget.components.AIStateLoading
import kotlinx.coroutines.flow.distinctUntilChanged

@Composable
fun AudioToTextResultPanel(
    aiLoadingStatus: AudioResult<AudioStreamingMsg>?,
    onRetryClick: () -> Unit,
    onCopyClick: () -> Unit,
    onStopClick: (Boolean, () -> Unit) -> Unit,
    onTryAgainClick: () -> Unit,
    onOutput: (List<AudioTransferEntity>) -> Unit,
    modifier: Modifier,
    showToast: (toastId: Int) -> Unit = {},
    isHalfScreen: Boolean = true,
    isPhone: Boolean = BuildConfig.IS_PHONE,
    isOffline: Boolean = false,
) {
    LaunchedEffect(key1 = aiLoadingStatus) {
        if (aiLoadingStatus is AudioResult.Success && aiLoadingStatus.data.status != AudioStreamingStatus.IN_PROGRESS) {
            onOutput(aiLoadingStatus.data.transfers)
        }
    }
    val clipboardManager: ClipboardManager = LocalClipboardManager.current

    val loadingTip = if (isTablet) stringResource(id = R.string.creating_in_progress) else ""
    AICommonResultPanelColumn() {
        if (aiLoadingStatus != null) {
            when (aiLoadingStatus) {
                is AudioResult.Loading -> {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        AIStateLoading(
                            isOffline = isOffline,
                            isRowLayout = isTablet,
                            tip = loadingTip,
                            onStopClick = {
                                onStopClick(true) {
                                    showToast(R.string.stopped_answer)
                                }
                            })
                    }
                }

                is AudioResult.Success -> {
                    AudioToTextSuccessContent(
                        aiLoadingStatus,
                        isOffline,
                        loadingTip,
                        onStopClick,
                        showToast,
                        isPhone,
                        onRetryClick,
                        clipboardManager,
                        onCopyClick
                    )

                }

                else -> {
                    ToTextFailed(isPhone, isHalfScreen, onTryAgainClick)
                }
            }
        } else {

            Row(
                modifier = Modifier
                    .padding(bottom = 6.dp)
                    .weight(1f), verticalAlignment = Alignment.Bottom
            ) {
                Spacer(modifier = Modifier.weight(1f))

                Image(
                    painter = painterResource(id = R.drawable.ai_reweite_retry),
                    contentDescription = stringResource(id = R.string.retry),
                    modifier = Modifier
                        .padding(end = 8.dp)
                        .clickable(
                            interactionSource = null,
                            indication = ripple(bounded = false)
                        ) {
                            onRetryClick.invoke()
                        })

                Image(
                    painter = painterResource(id = R.drawable.writing_copy),
                    contentDescription = stringResource(id = R.string.writing_copy),
                    alpha = 0.5f
                )
            }
        }
        if (isPhone && aiLoadingStatus !is AudioResult.Loading &&
            !(aiLoadingStatus is AudioResult.Success
                    && aiLoadingStatus.data.status == AudioStreamingStatus.IN_PROGRESS)
        ) {
            Spacer(modifier = Modifier.height(12.dp))
            BottomGeneratedView()
        }
    }
}

@Composable
private fun ColumnScope.AudioToTextSuccessContent(
    aiLoadingStatus: AudioResult.Success<AudioStreamingMsg>,
    isOffline: Boolean,
    loadingTip: String,
    onStopClick: (Boolean, () -> Unit) -> Unit,
    showToast: (toastId: Int) -> Unit,
    isPhone: Boolean,
    onRetryClick: () -> Unit,
    clipboardManager: ClipboardManager,
    onCopyClick: () -> Unit
) {
    val lazyListState = rememberLazyListState()
    var isAtBottom by remember { mutableStateOf(false) }
    Box(
        modifier = Modifier.Companion
            .weight(1f)
            .fillMaxWidth()
    ) {
        // Observe the scroll position changes
        LaunchedEffect(lazyListState) {
            snapshotFlow { lazyListState.layoutInfo.visibleItemsInfo.lastOrNull()?.index }
                .distinctUntilChanged()
                .collect { lastVisibleItemIndex ->
                    // Check if the last visible item is the last item in the list
                    isAtBottom =
                        lastVisibleItemIndex == lazyListState.layoutInfo.totalItemsCount - 1
                }
        }
        val accessibilityScrollToSeeMore = stringResource(
            id = R.string.accessibility_scroll_to_see_more // 需要添加这个字符串资源："向下滑动查看更多内容"
        )
        LazyColumn(
            state = lazyListState,
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = 52.dp, top = 16.dp)
                .semantics {
                    // 当有更多内容时，添加可滚动提示
                    if (lazyListState.layoutInfo.totalItemsCount > lazyListState.layoutInfo.visibleItemsInfo.size) {
                        stateDescription = accessibilityScrollToSeeMore
                    }
                    focused = true
                }
        ) {
            itemsIndexed(aiLoadingStatus.data.transfers) { index, entity ->
                TransferItem(
                    position = index,
                    entity = entity,
                )
            }
        }
        if (!isAtBottom) {
            // 在列表的底部添加渐隐效果区
            BottomGradient(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(bottom = 48.dp)
            )
        }
        if (aiLoadingStatus.data.status == AudioStreamingStatus.IN_PROGRESS) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.fillMaxWidth()
            ) {
                AIStateLoading(
                    isOffline = isOffline,
                    isRowLayout = isTablet,
                    tip = loadingTip,
                    onStopClick = {
                        onStopClick(true) {
                            showToast(R.string.stopped_answer)
                        }
                    })
            }
        } else {
            Row(
                modifier = Modifier
                    .align(Alignment.BottomCenter),
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (!isPhone) {
                    BottomGeneratedView()
                }
                Spacer(modifier = Modifier.weight(1f))

                Image(
                    painter = painterResource(id = R.drawable.ic_retry),
                    contentDescription = stringResource(id = R.string.retry),
                    modifier = Modifier
                        .padding(end = 8.dp)
                        .clickable(
                            interactionSource = null,
                            indication = ripple(bounded = false)
                        ) {
                            onRetryClick.invoke()
                        })

                Image(
                    painter = painterResource(id = R.drawable.ic_copy),
                    contentDescription = stringResource(id = R.string.writing_copy),
                    modifier = Modifier.clickable(
                        interactionSource = null,
                        indication = ripple(bounded = false)
                    ) {
                        val clipData = AnnotatedString(
                            aiLoadingStatus.data.transfers.joinToString("\n") {
                                it.content
                            }
                        )
                        clipboardManager.setText(clipData)
                        onCopyClick.invoke()
                        showToast(R.string.copied)
                    })
            }
        }
    }
}

@Composable
private fun ToTextFailed(
    isPhone: Boolean,
    isHalfScreen: Boolean,
    onTryAgainClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(top = if (!isPhone) 0.dp else if (isHalfScreen) 0.dp else 45.dp),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painter = painterResource(id = com.tcl.ai.note.voicetotext.R.drawable.icon_generator_failed),
                contentDescription = stringResource(id = R.string.text_audio_to_text_failed),
                modifier = Modifier
                    .accessibilityDescription(stringResource(id = R.string.text_audio_to_text_failed))
                    .size(if (isPhone) 111.dp else 114.dp)
            )
            val textFailed =
                stringResource(id = R.string.text_audio_to_text_failed)
            Text(
                text = textFailed,
                fontSize = 16.sp,
                color = TclTheme.tclColorScheme.tctStanderTextSecondary,
                modifier = Modifier.accessibilityDescription(textFailed)

            )
        }

        Row(
            modifier = Modifier
                .padding()
                .height(40.dp)
                .clip(RoundedCornerShape(24.dp))
                .background(
                    colorResource(id = R.color.tct_stander_accent_primary_button_new)
                )
                .clickable(role = Role.Button) {
                    onTryAgainClick.invoke()
                }
                .padding(start = 32.dp, end = 32.dp)
                .align(Alignment.CenterHorizontally),
            verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = stringResource(id = R.string.text_try_again),
                fontSize = 14.sp,
                modifier = Modifier
                    .padding(start = 6.dp),
                color = Color.White
            )
        }
    }
}

@Composable
private fun BottomGeneratedView(isPhone: Boolean = BuildConfig.IS_PHONE) {
    val aiContentPrompt = stringResource(id = R.string.content_prompt)
    Column(
        modifier = if (isPhone) Modifier
            .fillMaxWidth()
            .accessibilityDescription(aiContentPrompt) else Modifier
            .wrapContentWidth()
            .accessibilityDescription(aiContentPrompt)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.align(Alignment.CenterHorizontally),
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_ai_bottom_prompt),
                modifier = Modifier.size(42.fdp),
                contentDescription = null,
            )
            Spacer(Modifier.width(2.dp))
            Text(
                text = aiContentPrompt,
                textAlign = TextAlign.Center,
                fontSize = 9.sp,
                fontWeight = FontWeight.W500,
                lineHeight = 28.sp,
                color = colorResource(id = R.color.tct_btn_checked_disable_primary_color),
                modifier = Modifier
            )
        }
    }
}

@Composable
private fun TransferItem(
    position: Int,
    entity: AudioTransferEntity,
    isPhone: Boolean = BuildConfig.IS_PHONE
) {
    Box(
        modifier = Modifier.fillMaxWidth()
    ) {
        val speakerText =
            String.format(LocalContext.current.getString(R.string.text_speaker), entity.personNum)
        Column {
            if (position > 0) {
                Spacer(modifier = Modifier.height(12.dp))
            }
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = speakerText,
                    fontWeight = FontWeight.Bold,
                    fontSize = if (isPhone) 16.sp else 14.sp,
                    modifier = Modifier.accessibilityDescription(speakerText)
                )
                Spacer(modifier = Modifier.padding(start = 13.dp))
                val timeText = formatAudioTime(entity.startTime)
                Text(
                    text = timeText,
                    fontSize = if (isPhone) 16.sp else 14.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.accessibilityDescription(timeText)
                )
            }
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = entity.content,
                lineHeight = if (isPhone) 28.sp else 20.sp,
                fontSize = if (isPhone) 16.sp else 14.sp,
                modifier = Modifier.accessibilityDescription(entity.content)
            )
        }
    }
}

@Composable
fun BottomGradient(
    modifier: Modifier = Modifier,
    colors: List<Color> = listOf(Color.Transparent, TclTheme.colorScheme.tctResultBgColor),
    height: Dp = 60.dp
) {
    val heightPx = with(LocalDensity.current) { height.toPx() }

    Canvas(
        modifier = modifier
            .height(height)
            .fillMaxSize()
    ) {
        drawRect(
            brush = Brush.verticalGradient(
                colors = colors,
                startY = size.height - heightPx,
                endY = size.height
            ),
            size = size
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun AiResultPanelPreview() {
    val successState = getAudioTestSuccessState()
    AudioToTextResultPanel(successState, {}, {}, { it, callback -> }, {}, {}, Modifier, { id -> })
}

@Preview(device = "spec:width=1280dp,height=250dp")
@Composable
private fun AiResultPanelFailedPreview() {
    AudioToTextResultPanel(
        audioErrorState,
        {},
        {},
        { it, callback -> },
        {},
        {},
        Modifier,
        { id -> })
}

@Preview(showBackground = true)
@Composable
private fun TransferItemPreview() {
    TransferItem(
        0,
        entity = AudioTransferEntity(
            0,
            "",
            "哈Hello World 豪华饭店客房好看的话还是哈哈哈哈哈哈哈",
            0,
            100,
            "1"
        )
    )
}