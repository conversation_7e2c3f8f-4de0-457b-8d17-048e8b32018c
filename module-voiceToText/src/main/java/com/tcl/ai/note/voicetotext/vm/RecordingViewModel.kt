package com.tcl.ai.note.voicetotext.vm

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.utils.AppActivityManager
import com.tcl.ai.note.voicetotext.audio.ShowAudioRecorder
import com.tcl.ai.note.voicetotext.util.AudioToTextConstant
import com.tcl.ai.note.voicetotext.data.AudioRepository
import com.tcl.ai.note.voicetotext.intent.RecordIntent
import com.tcl.ai.note.voicetotext.states.RecordSpecialState
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.voicetotext.audio.RecordingForegroundService
import com.tcl.ai.note.voicetotext.util.deleteFile
import com.tcl.ai.note.voicetotext.states.RecordingState
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class RecordingViewModel @Inject constructor(@ApplicationContext val context: Context, private val repository: AudioRepository) : ViewModel() {

    private val _recordState = MutableStateFlow(RecordingState())
    val recordState = _recordState.asStateFlow()

    init {
        // 设置为测试模式，方便测试
//        AudioToTextConstant.useTestMode()
        // 在生产环境中应该使用
         AudioToTextConstant.useProductionMode()
        Logger.d(TAG, "Using test mode for recording time configuration")

        observeRecordingState()
        viewModelScope.launch {
            AppActivityManager.isLeaveHintStateFlow.collect {
                Logger.d(TAG, "isLeaveHintStateFlow: $it, isAddingImage: ${AppActivityManager.isAddingImage.value}")
                if (it && !AppActivityManager.isAddingImage.value) {
                    Logger.d(TAG, "AppActivity is leave hint, stopRecord")
                    stopRecord()
                }
            }
        }
    }

    fun recordingIntent(intent: RecordIntent) {
        when (intent) {
            is RecordIntent.StartRecord -> {
                Logger.d(TAG, "StartRecord")
                startRecording(intent.audioPath)
            }
            is RecordIntent.SaveRecord -> {
                Logger.d(TAG, "SaveRecord isRecording: ${ShowAudioRecorder.isRecording}")
                if (ShowAudioRecorder.isRecording) {
                    val duration = ShowAudioRecorder.stopRecord()
                    if (duration > AudioToTextConstant.MIN_RECORD_DURATION) {
                        updateRecordingState(RecordingState(isRecording = false,
                            recordingIconVisible = false, recordDuration = duration, audioPath = ShowAudioRecorder.audioFilePath))
                    } else {
                        deleteAudioFile(ShowAudioRecorder.audioFilePath)
                        updateRecordingState(RecordingState(isRecording = false, audioPath = ShowAudioRecorder.audioFilePath,
                            specialState = RecordSpecialState.RecordingError(context.getString(com.tcl.ai.note.base.R.string.edit_audio_record_shortest_time))))
                    }
                }
            }
            is RecordIntent.StopRecord -> {
                Logger.d(TAG, "StopRecord")
                stopRecord()
            }
        }
    }

    private fun observeRecordingState() {
        viewModelScope.launch {
            ShowAudioRecorder.recordingState.collect {
                when (it.specialState) {
                    is RecordSpecialState.RecordingError -> {
                        deleteAudioFile(it.audioPath)
                    }

                    RecordSpecialState.AudioFocusLost -> {
                        Logger.d(TAG, "AudioFocusLost, stopRecord")
                        stopRecord()
                    }

                    RecordSpecialState.MaxDurationReached -> {
                        Logger.d(TAG, "MaxDurationReached, stopRecord")
                        // 当录音达到最大时长时，停止录音
                         stopRecord()
                    }

                    null -> {

                    }
                }
                updateRecordingState(it)
            }
        }
    }

    private fun startRecording(audioPath: String) {
        if (!ShowAudioRecorder.isRecording) {
            viewModelScope.launch {
                //ShowAudioRecorder.startRecord()
                RecordingForegroundService.startServiceForStartRecording(audioPath)
            }
        }
    }

    fun stopRecord() {
        Logger.d(TAG, "stopRecord")
        recordingIntent(RecordIntent.SaveRecord)
        RecordingForegroundService.stopRecordingService()
    }

    private fun updateRecordingState(recordingState: RecordingState) {
        _recordState.value = recordingState
    }

    fun resetRecordingState() {
        updateRecordingState(RecordingState())
    }

    fun deleteAudioFile(audioPath: String?) {
        viewModelScope.launchIO {
            deleteFile(audioPath)
            audioPath?.let {
                repository.deleteTransfersByAudioPath(audioPath)
            }
        }
    }

    companion object {
        private const val TAG = "RecordingViewModel"
    }

    /**
     * 当ViewModel被清除时调用，例如在应用被杀掉时
     * 确保停止所有正在进行的录音
     */
    override fun onCleared() {
        super.onCleared()
        Logger.d(TAG, "onCleared: 停止录音状态")

//        // 停止录音服务
        if (ShowAudioRecorder.isRecording) {
            stopRecord()
        }
    }
}