<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.tcl.ai.note.hwtt.demo.DrawingView
        android:id="@+id/drawing_view"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_weight="1"
        android:background="#80FFFFFF" />
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            <com.tcl.ai.note.hwtt.demo.StatusTextView
                android:id="@+id/status_text_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Status text..."
                android:textIsSelectable="true" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/download_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="downloadClick"
                    android:text="Download" />
                <Button
                    android:id="@+id/recognize_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="recognizeClick"
                    android:text="Recognize" />
                <Button
                    android:id="@+id/clear_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="clearClick"
                    android:text="Clear" />
            </LinearLayout>
        </LinearLayout>
    </FrameLayout>
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:orientation="horizontal">
            <Button
                android:id="@+id/delete_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:onClick="deleteClick"
                android:text="Delete model" />
        </LinearLayout>
    </FrameLayout>
</LinearLayout>