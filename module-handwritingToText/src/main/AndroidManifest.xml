<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.INTERNET" />



    <application>
        <activity
            android:name="com.tcl.ai.note.hwtt.view.HandwritingToTextActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:configChanges="orientation|screenSize|keyboardHidden|uiMode"
            android:screenOrientation="portrait"
            android:theme="@style/TransparentTheme">
        </activity>
    </application>

</manifest>