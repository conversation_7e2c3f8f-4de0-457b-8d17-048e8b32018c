package com.tcl.ai.note.utils

import android.util.Log

object Logger {
    fun d(tag: String, message: String) {
        if (Log.DEBUG >= MIN_LEVEL) {
            Log.d("${TAG}_${tag}", message)
        }
    }

    fun e(tag: String, message: String) {
        if (Log.ERROR >= MIN_LEVEL) {
            Log.e("${TAG}_${tag}", message)
        }
    }

    fun w(tag: String, message: String) {
        if (Log.WARN >= MIN_LEVEL) {
            Log.w("${TAG}_${tag}", message)
        }
    }

    fun v(tag: String, message: String) {
        if (Log.VERBOSE >= MIN_LEVEL) {
            Log.v("${TAG}_${tag}", message)
        }
    }

    fun i(tag: String, message: String) {
        if (Log.INFO >= MIN_LEVEL) {
            Log.i("${TAG}_${tag}", message)
        }
    }

    private const val TAG = "Note"
    private const val MIN_LEVEL = Log.VERBOSE
}