package com.tcl.ai.note.utils

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import androidx.lifecycle.lifecycleScope
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.BuildConfig
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.track.TclAnalytics

val APP_PACKAGE_NAME = if(BuildConfig.IS_PHONE) "com.tcl.ai.note" else "com.tcl.ai.note.hd"

fun Context.startAISummary(noteId: Long) {
    val intent = Intent().apply {
        component = ComponentName(APP_PACKAGE_NAME, "com.tcl.ai.note.summary.view.SummaryActivity")
        putExtra("noteId", noteId)
        flags = Intent.FLAG_ACTIVITY_NEW_TASK
    }
    startActivity(intent)
}

fun Context.startAIPolish(noteId: Long) {
    val intent = Intent().apply {
        component = ComponentName(APP_PACKAGE_NAME, "com.tcl.ai.note.polish.view.ReWriteActivity")
        putExtra("noteId", noteId)
        flags = Intent.FLAG_ACTIVITY_NEW_TASK
    }
    startActivity(intent)
}

fun Context.startAIHelpWriting(noteId: Long) {
    val intent = Intent().apply {
        component = ComponentName(APP_PACKAGE_NAME, "com.tcl.ai.note.helpwriting.ui.HelpMeWriteActivity")
        putExtra("noteId", noteId)
        flags = Intent.FLAG_ACTIVITY_NEW_TASK
    }
    startActivity(intent)
}

/**
 * activity跳转防抖 默认1000毫秒
 * 简化版本：使用单一时间戳，不区分不同的key
 * 修复：防止防抖时间累计
 */
object ActivityDebounce {
    private var lastClickTime = 0L
    private const val DEBOUNCE_TIME = 1000L

    fun checkDebounce(): Boolean {
        val currentTime = System.currentTimeMillis()
        val timeDiff = currentTime - lastClickTime
        val result = timeDiff >= DEBOUNCE_TIME

        Logger.d("ActivityDebounce", "currentTime=$currentTime, lastTime=$lastClickTime, diff=$timeDiff, result=$result")

        // 无论是否通过防抖检查，都更新lastClickTime
        // 这样可以防止防抖时间累计
        lastClickTime = currentTime

        return result
    }
}

fun Context.startHandwritingToText(noteId: Long) {
    if (!ActivityDebounce.checkDebounce()) {
        return
    }
    Logger.d("startHandwritingToText","noteId = $noteId time = ${System.currentTimeMillis()}")
    val intent = Intent().apply {
        component =
            ComponentName(APP_PACKAGE_NAME, "com.tcl.ai.note.hwtt.view.HandwritingToTextActivity")
        putExtra("NoteId", noteId)
        flags = Intent.FLAG_ACTIVITY_NEW_TASK
    }
    startActivity(intent)
}

/**
 * 启动音频转文本结果展示
 * @param audioPath 音频文件的路径
 */
fun Context.startAudioToText(audioPath: String){
    val intent = Intent().apply {
        component = ComponentName(APP_PACKAGE_NAME, "com.tcl.ai.note.voicetotext.view.AudioToTextActivity")
        putExtra("audioPath", audioPath)
        flags = Intent.FLAG_ACTIVITY_NEW_TASK
    }
    startActivity(intent)
}

/**
 * 启动TCL AI 充值界面
 */
fun Context.startTCLAIChargeActivity() {
    val intent = Intent().apply {
        action = "com.tcl.ai.action.SETTINGS_MENU_GUIDE"
        `package` = "com.tcl.ai.app"
        putExtra("navigate_to", "vip_settings")
        flags = Intent.FLAG_ACTIVITY_NEW_TASK
    }
    startActivity(intent)
}
fun Context.startRichTextDemoActivity(){
    val intent = Intent().apply {
        component = ComponentName(APP_PACKAGE_NAME, "com.tcl.ai.note.handwritingtext.demo.RichTextDemoActivity")
        component = ComponentName(APP_PACKAGE_NAME, "com.tcl.ai.note.handwritingtext.demo.RichAreEditDemoActivity")
        flags = Intent.FLAG_ACTIVITY_NEW_TASK
    }
    startActivity(intent)
}