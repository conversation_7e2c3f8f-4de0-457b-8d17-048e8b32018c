<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="20dp"
    android:height="20dp"
    android:viewportWidth="20"
    android:viewportHeight="20">
  <path
      android:pathData="M0,0h20v20h-20z"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.01"/>
  <path
      android:pathData="M5.833,3.333C4.913,3.333 4.167,4.08 4.167,5L4.167,16.667L14.167,16.667C15.087,16.667 15.833,15.92 15.833,15L15.833,3.333L5.833,3.333ZM14.583,4.583L5.833,4.583Q5.417,4.583 5.417,5L5.417,15.417L14.167,15.417Q14.583,15.417 14.583,15L14.583,4.583Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="15.833"
          android:startY="10"
          android:endX="4.167"
          android:endY="10"
          android:type="linear">
        <item android:offset="0" android:color="#FFC95EFF"/>
        <item android:offset="1" android:color="#FF436BF1"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M6.667,5.833h6.667v1.25h-6.667z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="13.334"
          android:startY="6.458"
          android:endX="6.667"
          android:endY="6.458"
          android:type="linear">
        <item android:offset="0" android:color="#FFC95EFF"/>
        <item android:offset="1" android:color="#FF436BF1"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M6.667,8.333h6.667v1.25h-6.667z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="13.334"
          android:startY="8.958"
          android:endX="6.667"
          android:endY="8.958"
          android:type="linear">
        <item android:offset="0" android:color="#FFC95EFF"/>
        <item android:offset="1" android:color="#FF436BF1"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M6.667,10.833h6.667v1.25h-6.667z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="13.334"
          android:startY="11.458"
          android:endX="6.667"
          android:endY="11.458"
          android:type="linear">
        <item android:offset="0" android:color="#FFC95EFF"/>
        <item android:offset="1" android:color="#FF436BF1"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M6.667,13.333h3.333v1.25h-3.333z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="10"
          android:startY="13.958"
          android:endX="6.667"
          android:endY="13.958"
          android:type="linear">
        <item android:offset="0" android:color="#FFC95EFF"/>
        <item android:offset="1" android:color="#FF436BF1"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
