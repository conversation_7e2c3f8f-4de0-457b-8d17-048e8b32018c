<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <group>
    <clip-path
        android:pathData="M0,0h24v24h-24z"/>
    <path
        android:pathData="M20.74,7.12C20.181,5.61 18.99,4.419 17.48,3.86C18.99,3.301 20.181,2.11 20.74,0.6C21.299,2.11 22.49,3.301 24,3.86C22.49,4.419 21.299,5.61 20.74,7.12ZM0.089,3.173C-0.296,2.26 0.642,1.35 1.543,1.763L22.931,11.566C23.777,11.953 23.777,13.155 22.931,13.542L1.543,23.345C0.642,23.758 -0.296,22.848 0.089,21.935L3.115,14.747C3.262,14.397 3.581,14.15 3.956,14.094L12.428,12.823C12.737,12.776 12.737,12.332 12.428,12.285L3.956,11.014C3.581,10.958 3.262,10.711 3.115,10.361L0.089,3.173Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="-0.296"
            android:startY="12.179"
            android:endX="24"
            android:endY="12.179"
            android:type="linear">
          <item android:offset="0" android:color="#FF2788FF"/>
          <item android:offset="1" android:color="#FFF07DFF"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
