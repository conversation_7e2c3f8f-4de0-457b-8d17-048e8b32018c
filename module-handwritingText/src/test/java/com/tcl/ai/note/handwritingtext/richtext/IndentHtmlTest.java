package com.tcl.ai.note.handwritingtext.richtext;

import android.content.Context;
import android.text.Editable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;

import com.tcl.ai.note.handwritingtext.richtext.inner.Html;
import com.tcl.ai.note.handwritingtext.richtext.spans.AreLeadingMarginSpan;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;

import static org.junit.Assert.*;

/**
 * 测试ARE_IndentRight的Span与HTML属性之间的转换
 */
@RunWith(RobolectricTestRunner.class)
public class IndentHtmlTest {

    @Mock
    private Context mockContext;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        Html.sContext = RuntimeEnvironment.application;
    }

    @Test
    public void testAreLeadingMarginSpanGetHtml() {
        // 测试AreLeadingMarginSpan的getHtml方法
        AreLeadingMarginSpan span = new AreLeadingMarginSpan(Html.sContext);
        
        // 测试level为0的情况
        span.setLevel(0);
        assertEquals("", span.getHtml());
        
        // 测试level为1的情况
        span.setLevel(1);
        assertEquals(" style=\"margin-left: 2em;\"", span.getHtml());
        
        // 测试level为2的情况
        span.setLevel(2);
        assertEquals(" style=\"margin-left: 4em;\"", span.getHtml());
        
        // 测试level为3的情况
        span.setLevel(3);
        assertEquals(" style=\"margin-left: 6em;\"", span.getHtml());
    }

    @Test
    public void testSpannedToHtml() {
        // 测试将带有AreLeadingMarginSpan的Spanned转换为HTML
        SpannableStringBuilder builder = new SpannableStringBuilder("这是一段缩进的文本");
        
        AreLeadingMarginSpan span = new AreLeadingMarginSpan(Html.sContext);
        span.setLevel(2);
        
        builder.setSpan(span, 0, builder.length(), Spanned.SPAN_PARAGRAPH);
        
        String html = Html.toHtml(builder, Html.TO_HTML_PARAGRAPH_LINES_INDIVIDUAL);
        
        // 验证HTML包含margin-left样式
        assertTrue("HTML应该包含margin-left样式", html.contains("margin-left:4em;"));
        assertTrue("HTML应该包含文本内容", html.contains("这是一段缩进的文本"));
    }

    @Test
    public void testHtmlToSpanned() {
        // 测试将包含margin-left样式的HTML转换为Spanned
        String html = "<p style=\"margin-left:4em;\">这是一段缩进的文本</p>";
        
        Spanned spanned = Html.fromHtml(html, Html.FROM_HTML_MODE_COMPACT);
        
        // 验证生成的Spanned包含AreLeadingMarginSpan
        AreLeadingMarginSpan[] spans = spanned.getSpans(0, spanned.length(), AreLeadingMarginSpan.class);
        
        assertNotNull("应该包含AreLeadingMarginSpan", spans);
        assertTrue("应该至少有一个AreLeadingMarginSpan", spans.length > 0);
        
        AreLeadingMarginSpan span = spans[0];
        assertEquals("缩进级别应该为2", 2, span.getLevel());
    }

    @Test
    public void testHtmlToSpannedWithDifferentUnits() {
        // 测试不同单位的margin-left值
        
        // 测试em单位
        String htmlEm = "<p style=\"margin-left:6em;\">缩进文本</p>";
        Spanned spannedEm = Html.fromHtml(htmlEm, Html.FROM_HTML_MODE_COMPACT);
        AreLeadingMarginSpan[] spansEm = spannedEm.getSpans(0, spannedEm.length(), AreLeadingMarginSpan.class);
        if (spansEm.length > 0) {
            assertEquals("6em应该对应级别3", 3, spansEm[0].getLevel());
        }
        
        // 测试px单位
        String htmlPx = "<p style=\"margin-left:80px;\">缩进文本</p>";
        Spanned spannedPx = Html.fromHtml(htmlPx, Html.FROM_HTML_MODE_COMPACT);
        AreLeadingMarginSpan[] spansPx = spannedPx.getSpans(0, spannedPx.length(), AreLeadingMarginSpan.class);
        if (spansPx.length > 0) {
            assertEquals("80px应该对应级别2", 2, spansPx[0].getLevel());
        }
    }

    @Test
    public void testRoundTripConversion() {
        // 测试往返转换：Spanned -> HTML -> Spanned
        SpannableStringBuilder originalBuilder = new SpannableStringBuilder("测试往返转换");
        
        AreLeadingMarginSpan originalSpan = new AreLeadingMarginSpan(Html.sContext);
        originalSpan.setLevel(3);
        
        originalBuilder.setSpan(originalSpan, 0, originalBuilder.length(), Spanned.SPAN_PARAGRAPH);
        
        // 转换为HTML
        String html = Html.toHtml(originalBuilder, Html.TO_HTML_PARAGRAPH_LINES_INDIVIDUAL);
        
        // 再转换回Spanned
        Spanned convertedSpanned = Html.fromHtml(html, Html.FROM_HTML_MODE_COMPACT);
        
        // 验证转换后的Spanned包含正确的缩进级别
        AreLeadingMarginSpan[] convertedSpans = convertedSpanned.getSpans(0, convertedSpanned.length(), AreLeadingMarginSpan.class);
        
        assertNotNull("转换后应该包含AreLeadingMarginSpan", convertedSpans);
        assertTrue("转换后应该至少有一个AreLeadingMarginSpan", convertedSpans.length > 0);
        
        AreLeadingMarginSpan convertedSpan = convertedSpans[0];
        assertEquals("往返转换后缩进级别应该保持一致", 3, convertedSpan.getLevel());
    }
}
