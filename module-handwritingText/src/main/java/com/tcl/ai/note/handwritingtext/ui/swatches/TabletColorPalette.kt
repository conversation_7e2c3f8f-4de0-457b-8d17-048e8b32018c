package com.tcl.ai.note.handwritingtext.ui.swatches

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.bean.PenColor
import com.tcl.ai.note.handwritingtext.utils.borderCircle
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.swatches.ColorPaletteViewModel
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.getRichTextMenuDimens
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.widget.HoverProofIconButton


@Composable
fun TabletColorPalette(
    modifier: Modifier = Modifier,
    viewModel: ColorPaletteViewModel  = hiltViewModel(),
    suniaDrawViewModel: SuniaDrawViewModel = hiltViewModel(),
    onConfirm:(color:Color) ->Unit,
    onDismiss: () -> Unit,

){
    val dimens = getRichTextMenuDimens()
    val context = LocalContext.current

    val selectedColor = with(viewModel) {
        when (currentSelectorIdx.intValue) {
            0 -> selectedColor0
            1 -> selectedColor1
            else -> selectedColor2
        }.value
    }

    val recentColors by viewModel.recentColors.collectAsState()
    val alpha  =viewModel.colorAlpha.value

    Column(
        modifier = modifier
        .width(dimens.colorPaletteWidht)
        .wrapContentHeight()
        .background(color = TclTheme.colorScheme.reWriteExpandBg)
        .padding(horizontal = 8.38.dp)
        .padding(top = 16.75.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        ColorSwatchGrid(selColor = selectedColor) { color ->
            viewModel.updateSelColor(color)

        }
        Spacer(modifier = Modifier.height(8.38.dp))
        if(recentColors.isNotEmpty()){
            Row(
                modifier = Modifier.fillMaxWidth().height(16.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                /*IconThemeSwitcher(
                    btnSize = 16.dp,
                    painter = R.drawable.color_picker.drawableRes(),
                    onClick = {

                    },
                    contentDescription =R.drawable.color_picker.stringRes()
                )*/
                RecentColorsBar(
                    colors = recentColors,
                    selectedColor = selectedColor,
                    onColorClick = { color ->
                        viewModel.updateSelColor(color)
                    }
                )
            }
        }



        Spacer(modifier = Modifier.height(8.38.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {

            DiagonalSplitSquare(
                color = selectedColor,
                alpha =alpha
            )
            Spacer(modifier = Modifier.width(3.dp))
            Box(
                modifier = Modifier
                    .width(36.dp)
                    .height(18.dp)
                    .background(
                        color = Color(0xffD9D9D9),
                        shape = RoundedCornerShape(3.dp)),
                contentAlignment = Alignment.Center
            ){
                Text(
                    modifier = Modifier.fillMaxSize(),
                    text = "${(alpha*100).toInt()}%",
                    lineHeight = 18.sp,
                    fontSize = 10.sp,
                    textAlign = TextAlign.Center,
                    color = Color(0xff3D3D3D),
                )


            }

            Spacer(modifier = Modifier.width(8.38.dp))
            AlphaSlider(
                baseColor = selectedColor,
                alphaValue = alpha,
                onAlphaChanged = {
                    viewModel.updateColorAlpha(it)

                }
            )
        }
        Spacer(modifier = Modifier.height(8.38.dp))
        BottomButton{ isOk ->
            if(isOk){
                viewModel.pickColor(selectedColor)
                suniaDrawViewModel.changePenColor(penColor = PenColor(color =  selectedColor.toArgb(), alpha = (alpha*100).toInt()))
                onConfirm(selectedColor)
            }
            onDismiss()
        }
        Spacer(modifier = Modifier.height(8.38.dp))


    }
}
@Composable
fun BottomButton(
    modifier: Modifier = Modifier,
    onClick: (isOk: Boolean) -> Unit
){
    Row(
        modifier = modifier.fillMaxWidth().height(20.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = R.string.cancel.stringRes(),
            fontSize = 13.sp,
            lineHeight = 18.sp,
            color = Color.Black,
            textAlign = TextAlign.Center,
            modifier = Modifier.width(60.dp)
                .clickable {
                    onClick(false)
                }
        )
        Spacer(modifier = Modifier.weight(1f))
        Text(
            text = R.string.confirm.stringRes(),
            fontSize = 13.sp,
            lineHeight = 18.sp,
            color = Color.Black,
            textAlign = TextAlign.Center,
            modifier = Modifier.width(60.dp)
                .clickable {
                    onClick(true)
                }
        )
    }
}


@Composable
fun DiagonalSplitSquare(
    color: Color,
    alpha: Float,
    sizeDp: Int = 18
) {
    val cornerRadius = 3.dp
    Canvas(modifier = Modifier.size(sizeDp.dp).background(
        color = Color.Transparent,
        shape = RoundedCornerShape(cornerRadius)
    ).clip(RoundedCornerShape(cornerRadius)) ) {
        val width = size.width
        val height = size.height

        val color1Path = Path().apply {
            moveTo(0f, 0f)
            lineTo(0f, height)
            lineTo(width, 0f)
            close()
        }
        drawPath(color1Path, color = color)

        drawLine(
            color =color,
            start = Offset(width, 0f),
            end = Offset(0f, height),
            strokeWidth = 1.dp.toPx()
        )
        val color2Path = Path().apply {
            moveTo(0f, height)
            lineTo(width, height)
            lineTo(width, 0f)
            close()
        }
        drawPath(color2Path, color = color,alpha = alpha)
    }

}

/**
 * 个人收藏颜色栏
 */
@Composable
fun RecentColorsBar(
    colors: List<Color>,
    selectedColor: Color,
    onColorClick: (Color) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(6.5.dp)
    ) {
        colors.forEach { color ->
            val hsl = color.toHslArray()
            val desc = getColorDescription(context,hsl[0], hsl[1], hsl[2])
            HoverProofIconButton (
                modifier = Modifier
                    .invisibleSemantics()
                    .size(16.dp),
                onClick = {
                    onColorClick(color)
                }
            ) {
                Box(
                    modifier = Modifier
                        .semantics {
                            this.contentDescription =context.getString(R.string.color_swatch_collection).plus(desc)
                            this.role = Role.Button
                        }
                        .size(16.dp)
                        .drawBehind {
                            if (color == selectedColor) {
                                drawCircle(
                                    color = (color == Color.White).judge(Color.Black,color),
                                    style = Stroke(width = 1.5.dp.toPx())
                                )
                            }

                            if (color == Color.White && color != selectedColor) {
                                drawCircle(
                                    color = Color.LightGray,
                                    style = Stroke(width =0.7.dp.toPx())
                                )
                            }
                        }
                        .padding((color == selectedColor).judge(3,0).dp)
                        .then((color == Color.White).judge(Modifier.borderCircle(),Modifier))
                        .background(color = color, shape = CircleShape)

                )
            }

        }
    }
}