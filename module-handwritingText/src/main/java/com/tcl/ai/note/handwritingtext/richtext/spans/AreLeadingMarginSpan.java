package com.tcl.ai.note.handwritingtext.richtext.spans;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.text.Layout;
import android.text.style.LeadingMarginSpan;

import com.tcl.ai.note.handwritingtext.richtext.inner.Constants;
import com.tcl.ai.note.handwritingtext.richtext.inner.Util;

public class AreLeadingMarginSpan implements LeadingMarginSpan, ARE_Span {

	// 使用两个汉字作为参考宽度
	private static final String REFERENCE_TEXT = "汉字";

	private int mStandardLeading;
	private Context mContext;
	private int mLevel = 0;

	private int mLeadingMargin = mStandardLeading;
	private int DefaultFontSize=12;//sp

	public AreLeadingMarginSpan(Context context) {
		mContext = context;
		mStandardLeading = calculateChineseCharacterWidth(context);
		mLeadingMargin = mStandardLeading;
	}

	@Override
	public int getLeadingMargin(boolean first) {
		return mLeadingMargin;
	}

	@Override
	public void drawLeadingMargin(Canvas c, Paint p, int x, int dir, int top,
                                  int baseline, int bottom, CharSequence text, int start, int end,
                                  boolean first, Layout layout) {
		c.drawText(Constants.ZERO_WIDTH_SPACE_STR, x + dir + mLeadingMargin, baseline, p);
	}

	/**
	 * Set leading level
	 *
	 * @param level
	 */
	public void setLevel(int level) {
		mLevel = level;
		mLeadingMargin = mStandardLeading * mLevel;
	}

	public int getLevel() {
		return mLevel;
	}

	/**
	 * Increase leading level.
	 */
	public void increaseLevel() {
		++mLevel;
		mLeadingMargin = mStandardLeading * mLevel;
	}

	/**
	 * Decrease leading level.
	 *
	 * @return
	 */
	public int decreaseLevel() {
		--mLevel;
		if (mLevel < 0) {
			mLevel = 0;
		}
		mLeadingMargin = mStandardLeading * mLevel;
		return mLevel;
	}

	/**
	 * 计算两个汉字的宽度
	 *
	 * @param context
	 * @return 两个汉字的像素宽度
	 */
	private int calculateChineseCharacterWidth(Context context) {
		Paint paint = new Paint();
		// 设置默认文本大小，可以根据需要调整
		paint.setTextSize(Util.getPixelByDp(context, DefaultFontSize)); //
		paint.setAntiAlias(true);

		// 测量两个汉字的宽度
		float textWidth = paint.measureText(REFERENCE_TEXT);

		return (int) Math.ceil(textWidth);
	}

	@Override
	public String getHtml() {
		if (mLevel <= 0) {
			return "";
		}
		// 使用CSS的margin-left来实现左缩进效果
		// 每个level对应2em的缩进（大约两个汉字的宽度）
		return String.format(" style=\"margin-left: %dem;\"", mLevel * 2);
	}
}
