package com.tcl.ai.note.handwritingtext.richtext.utils

import android.text.SpannableStringBuilder
import android.text.style.ParagraphStyle
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.io.*
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.view.View
import com.tcl.ai.note.handwritingtext.richtext.inner.Html
import com.tcl.ai.note.handwritingtext.richtext.spans.ListBulletSpan
import com.tcl.ai.note.handwritingtext.richtext.spans.ListNumberSpan
import com.tcl.ai.note.handwritingtext.richtext.spans.UpcomingListSpan
import java.util.*


object KTUtils {

    var rickTextHTML = """
       <todo>
<li>​农村男方家<u><b>不错不方便疾病传播疾病传播难分难解</b></u></li>
<li check=true  style="text-align:end;"><b><u>​</u></b><u><b><b><u>​好处合法化和办法帮你b</u></b></b></u></li>
<li check=true  style="text-align:end;">​<u><b>​</b></u>回复回复和警察局静静的</li>
<li check=true  style="text-align:end;">​​你才能烦恼</li>
</todo>
<p>补偿费和会很方便奶粉呢<span style="font-size:28px";>能否你猜呢你nncn保持补充包</span> </p>
<br><p>解放军 </p>
<br><p>Hfhfh how to get a很多好多好多回复回复和当红花旦弧度呵护 </p>
<br></todo><ul>
<li>​你猜呢才能你免费军军</li>
<li>​你猜呢才能你免费军军</li>
<li>​你猜呢才能你免费军军</li>
<li>​你猜呢才能你免费军军</li>
<li>​你猜呢才能你免费军军</li>
<li>​你猜呢才能你免费军军</li>
<li>​你猜呢才能你免费军军</li>
<li>​你猜呢才能你免费军军</li>
<li>​你猜呢才能你免费军军</li>
<li>​你猜呢才能你免费军军</li>
<li>​你猜呢才能你免费军军</li>
<li>​你猜呢才能你免费军军</li>
<li>​你猜呢才能你免费军军</li>
<li>​你猜呢才能你免费军军</li>
<li>​你猜呢才能你免费军军</li>
<li style="text-align:center;">​​奶粉呢跟你农村女你</li>
<li>​怒放你能否你麻烦你 </li>
</ul><ol>
<li><span style="font-size:28px";>​部分保持不变你办法帮你</span></li>
<li style="text-align:end;">​​君君</li>
<li>​每次女能男女 <b><u><i><span style="text-decoration:line-through;">你猜呢才能叫农村经济</span></i></u></b> </li>
</ol>
    """
    /**
     * 对象转字节数组
     */
    @Throws(IOException::class)
    fun objectToBytes(obj: Any?): ByteArray? {
        ByteArrayOutputStream().use { out ->
            ObjectOutputStream(out).use { sOut ->
                sOut.writeObject(obj)
                sOut.flush()
                return out.toByteArray()
            }
        }
    }

    /**
     * 字节数组转对象
     */
    @Throws(IOException::class, ClassNotFoundException::class)
    fun bytesToObject(bytes: ByteArray?): Any? {
        ByteArrayInputStream(bytes).use { `in` -> ObjectInputStream(`in`).use { sIn -> return sIn.readObject() } }
    }


    /**
     * Html转分享文字
     */
    @JvmStatic
    fun htmlToShareText(content: String): StringBuilder {
        val builder = StringBuilder()
        val spannableStringBuilder =
            Html.fromHtml(content, Html.FROM_HTML_MODE_COMPACT) as SpannableStringBuilder
        builder.append(spannableStringBuilder)
        val obj: Array<out ParagraphStyle>? = spannableStringBuilder.getSpans(
            0,
            spannableStringBuilder.length,
            ParagraphStyle::class.java
        )
        var addLength = 0
        obj?.let {
            for (ps in it) {
                val start: Int = spannableStringBuilder.getSpanStart(ps)
                when (ps) {
                    is UpcomingListSpan -> {
                        addLength += if (ps.isChecked) {
                            builder.insert(start + addLength, "[V]")
                            3
                        } else {
                            builder.insert(start + addLength, "[ ]")
                            3
                        }

                    }
                    is ListNumberSpan -> {
                        val numberStr = "${ps.number}."
                        builder.insert(start + addLength, numberStr)
                        addLength += numberStr.length
                    }
                    is ListBulletSpan -> {
                        builder.insert(start + addLength, "·")
                        addLength += 1
                    }
                }
            }
        }
        return builder
    }

    @JvmStatic
    fun deleteFile(pathname: String) {
        CoroutineScope(Dispatchers.Main).launch {
            val file = File(pathname)
            if (file.exists()) file.delete()
        }
    }


    /**
     * 创建一个OVAL GradientDrawable
     */
    fun getOvalGradientDrawable(color: Int): GradientDrawable {
        val roundRect = GradientDrawable()
        roundRect.shape = GradientDrawable.OVAL
        roundRect.setColor(color)
        roundRect.setStroke(DisplayUtils.dp2px(1), Color.parseColor("#EDEDED"))
        roundRect.setSize(DisplayUtils.dp2px(20), DisplayUtils.dp2px(20))
        return roundRect
    }
}

fun View.isVisible(isVisible: Boolean) {
    this.visibility = if (isVisible) {
        View.VISIBLE
    } else {
        View.GONE
    }
}