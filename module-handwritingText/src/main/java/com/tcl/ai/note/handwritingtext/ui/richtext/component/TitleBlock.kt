package com.tcl.ai.note.handwritingtext.ui.richtext.component

import android.annotation.SuppressLint
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.focusTarget
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.focus.onFocusEvent
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.input.key.type
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.bean.MenuTypes
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.intent.RichTextIntent
import com.tcl.ai.note.handwritingtext.state.RichTextState
import com.tcl.ai.note.handwritingtext.vm.RichTextViewModel
import com.tcl.ai.note.theme.editorTitleTextStyle
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.substringByCodePoints
import com.tcl.ai.note.utils.tryToRequestFocus

/**
 * 标题栏
 */
@SuppressLint("DesignSystem")
@Composable
fun TitleBlock(
    darkTheme: Boolean,
    onTitleChange: (String) -> Unit,
    onFocusChanged: (Boolean) -> Unit,
    title: String,
    modifier: Modifier = Modifier,
    onEnterPress: () -> Unit = {},
    focusRequester: FocusRequester = remember { FocusRequester() },
    richTextViewModel: RichTextViewModel = hiltViewModel(),
) {
    var titleText by remember { mutableStateOf(TextFieldValue(title)) }
    val state by richTextViewModel.state.collectAsState()
    LaunchedEffect(title) {
        val newText = (if (title.codePointCount(0, title.length) <= TITLE_MAX_LENGTH) title
        else substringByCodePoints(title, TITLE_MAX_LENGTH)).replace("\n", "")
        val safePos = if (state.titleCursorPosition >= 0)
            state.titleCursorPosition.coerceAtMost(newText.length)
        else
            newText.length
        titleText = TextFieldValue(newText, TextRange(safePos))
    }

    LaunchedEffect(state.titleCursorPosition, state.editMode,state.bottomMenuType) {
        // 是否手绘模式
        val isOnDraw = state.bottomMenuType == MenuBar.BRUSH
        if (
            !isOnDraw &&
            state.titleCursorPosition >= 0 &&
            state.focusedIndex == -1 &&
            state.editMode
        ) {
            focusRequester.tryToRequestFocus()
        }
    }

    BasicTextField(
        value = titleText,
        onValueChange = { newText ->
            val codePointLen = newText.text.codePointCount(0, newText.text.length)
            val trimmed = if (codePointLen > TITLE_MAX_LENGTH)
                substringByCodePoints(newText.text, TITLE_MAX_LENGTH)
            else newText.text
            val content = trimmed.replace("\n", "")
            val sel = minOf(newText.selection.start, content.length)

            val finalValue = TextFieldValue(content, TextRange(sel))
            titleText = finalValue
            onTitleChange(content)
            richTextViewModel.handleIntent(RichTextIntent.UpdateTitleCursorPosition(newText.selection.start))

            if (newText.text.endsWith("\n")) {
                handleEnterKey(richTextViewModel, state)
                onEnterPress.invoke()
            }
        },
        textStyle = editorTitleTextStyle.copy(
            color = darkTheme.judge(
                R.color.white.colorRes(),
                R.color.text_edit_color.colorRes()
            ).copy(alpha = 0.85f),
        ),
        cursorBrush = SolidColor(
            colorResource(
                darkTheme.judge(
                    R.color.white,
                    R.color.text_edit_color
                )
            )
        ),
        decorationBox = { innerTextField ->
            Box(
                contentAlignment = Alignment.CenterStart,
                modifier = Modifier.fillMaxWidth(),
            ) {
                if (titleText.text.isEmpty()) {
                    Text(
                        text = stringResource(R.string.title),
                        style = editorTitleTextStyle,
                        color = darkTheme.judge(
                            R.color.text_input_hint_dark,
                            R.color.text_input_hint
                        ).colorRes(),
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.CenterStart),
                    )
                }
                innerTextField()
            }
        },
        modifier = modifier
            .fillMaxWidth()
            .focusTarget()
            .focusRequester(focusRequester)
            .onFocusEvent { focusState ->
                onFocusChanged(focusState.hasFocus)
                if(focusState.hasFocus){
                    if(state.title.length<=titleText.text.length){
                        val cursorPosition = titleText.selection.start
                        richTextViewModel.handleIntent(RichTextIntent.UpdateTitleCursorPosition(cursorPosition))
                    }

                }
            }
            .onPreviewKeyEvent { keyEvent ->
                if (keyEvent.type == KeyEventType.KeyDown
                    && keyEvent.key == Key.Enter
                ) {
                    // 回车切换至正文开头
                    handleEnterKey(richTextViewModel,state)
                    onEnterPress.invoke()
                    true
                } else false
            },
    )
}

private fun handleEnterKey(richTextViewModel: RichTextViewModel,state:RichTextState) {
    if(state.contents.isNotEmpty()){
        when(val currentBlock = state.contents[0]){
            is EditorContent.TextBlock ->{
                if(currentBlock.text.text.isNotEmpty()){
                    richTextViewModel.handleIntent(RichTextIntent.AddContent(0, EditorContent.TextBlock(
                        TextFieldValue("")
                    )))
                }
            }
            is EditorContent.TodoBlock ->{
                if(currentBlock.text.text.isNotEmpty()){
                    richTextViewModel.handleIntent(RichTextIntent.AddContent(0, EditorContent.TextBlock(
                        TextFieldValue("")
                    )))
                }
            }
            else ->{
                richTextViewModel.handleIntent(RichTextIntent.AddContent(0, EditorContent.TextBlock(
                    TextFieldValue("")
                )))
            }
        }
    }
    richTextViewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(0))
}

private const val TITLE_MAX_LENGTH = 50