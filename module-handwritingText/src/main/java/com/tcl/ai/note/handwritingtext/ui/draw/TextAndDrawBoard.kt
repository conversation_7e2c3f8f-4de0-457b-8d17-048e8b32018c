package com.tcl.ai.note.handwritingtext.ui.draw

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.imePadding
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.widget.NestedScrollView
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.viewModelScope
import com.sunia.penengine.sdk.operate.canvas.ScaleInfo
import com.tcl.ai.note.handwritingtext.richtext.viewholder.RickTextViewHolder
import com.tcl.ai.note.handwritingtext.state.EditMode
import com.tcl.ai.note.handwritingtext.sunia.SuniaDrawViewController
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.widget.DisposableEffectWithLifecycle
import kotlinx.coroutines.launch

class TextAndDrawBoard(
    private val context: Context,
    private val lifecycleOwner: LifecycleOwner,
    private val suniaDrawViewModel: SuniaDrawViewModel,
    private val rickTextViewModel: RichTextViewModel2
) {
    val rickTextViewHolder= RickTextViewHolder(context, lifecycleOwner, rickTextViewModel)
    val textView = rickTextViewHolder.getView()
    val nestedScrollView = NestedScrollView(context)
    val suniaDrawViewController = SuniaDrawViewController(context, suniaDrawViewModel)
    fun attachViewGroup(viewGroup: ViewGroup): Unit {
        // viewGroup.addView(textView)
        viewGroup.addView(nestedScrollView.apply {
            addView(textView)
        })
        viewGroup.addView(suniaDrawViewController.holder)
    }

    init {
        suniaDrawViewModel.viewModelScope.launch {
            suniaDrawViewModel.scaleInfoShared.collect { scaleInfo ->
//                Logger.d(TAG, "scaleInfo: $scaleInfo")
                if (nestedScrollView.isAttachedToWindow) {
                    nestedScrollView.scrollTo(-scaleInfo.offsetX.toInt(), -scaleInfo.offsetY.toInt())
                    nestedScrollView.apply {
//                    translationX = scaleInfo.offsetX
//                    translationY = scaleInfo.offsetY
                        pivotX = scaleInfo.scaleCenterX
                        pivotY = scaleInfo.scaleCenterY
                        scaleX = scaleInfo.scale
                        scaleY = scaleInfo.scale
                    }
                }
            }
        }
    }

    companion object {
        const val TAG = "TextAndDrawBoard"
    }
}

@Composable
fun SuniaDrawBoard(
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    suniaDrawViewModel: SuniaDrawViewModel = hiltViewModel(),
    ricktTextViewModel: RichTextViewModel2 = hiltViewModel()
) {
    var textAndDrawBoard: TextAndDrawBoard? = remember { null }
    val focusManager = LocalFocusManager.current
    val lifecycleOwner = LocalLifecycleOwner.current
    DisposableEffectWithLifecycle(
        onPause = {
            Logger.d("SuniaDrawBoard", "onPause")
            focusManager.clearFocus()
            textAndDrawBoard?.apply {
                suniaDrawViewController.holder.visibility = View.INVISIBLE
            }
            textAndDrawBoard?.rickTextViewHolder?.unregisterTodoMessage()
        }
    )
    Logger.d("TextAndDrawBoard", "compose init")

    AndroidView(
        factory = { context ->
            val textAndDrawBoard = TextAndDrawBoard(
                context = context,
                lifecycleOwner,
                rickTextViewModel = ricktTextViewModel,
                suniaDrawViewModel = suniaDrawViewModel
            )
            FrameLayout(context).apply {
                textAndDrawBoard.attachViewGroup(this)
                textAndDrawBoard.rickTextViewHolder.registerTodoMessage()
            }
        },
        modifier = modifier.imePadding(),
        update = { view ->
//            view.isEnabled = enabled
            // view.enablePrediction(true)
        }
    )
}

@Composable
fun SuniaDrawDemo(
    modifier: Modifier = Modifier,
    suniaDrawViewModel: SuniaDrawViewModel = hiltViewModel(),
) {
    val editMode by suniaDrawViewModel.editModeState.collectAsState()

    Box(
        modifier = modifier.fillMaxSize()
    ) {
        SuniaDrawBoard(modifier = Modifier.fillMaxSize())
        Column {
            DemoRickTextTools()
            Button(onClick = {
                with(suniaDrawViewModel) {
                    changeEditMode(
                        if (editMode == EditMode.DRAW)
                            EditMode.TEXT
                        else EditMode.DRAW
                    )
                }
            }) {
                if (editMode == EditMode.DRAW)
                    Text("change to text")
                else
                    Text("change to draw")
            }
            Button(onClick = {
                suniaDrawViewModel.saveDraw()
            }) {
                Text("save")
            }
        }
    }
}