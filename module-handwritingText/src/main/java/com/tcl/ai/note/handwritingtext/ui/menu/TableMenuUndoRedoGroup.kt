package com.tcl.ai.note.handwritingtext.ui.menu

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem
import com.tcl.ai.note.handwritingtext.vm.MenuBarUiState
import com.tcl.ai.note.theme.getRichTextMenuDimens
import com.tcl.ai.note.widget.DelayedBackgroundIconButton
import com.tcl.ai.note.widget.VerticalLine


@Composable
fun TableMenuUndoRedoGroup(menuBarState: MenuBarUiState) {
    val canUndo = menuBarState.canUndo
    val canRedo = menuBarState.canRedo
    val dimens = getRichTextMenuDimens()
    val menuColorItems = remember(canUndo,canRedo) {
        val menuBars = listOf(
            MenuBarItem.Undo.apply {
                isEnabled = canUndo
                onClick = {}
            },
            MenuBarItem.Redo.apply {
                isEnabled = canRedo
                onClick = {}
            })
        menuBars
    }
    Row(
        modifier = Modifier.height(dimens.topBarHeight),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ){
        Spacer(Modifier.width(18.dp))
        VerticalLine()
        Spacer(Modifier.width(18.dp))
        menuColorItems.forEachIndexed { index, item ->
            Spacer(Modifier.width(12.dp))
            DelayedBackgroundIconButton(
                btnSize = item.btnSize,
                painter = painterResource(item.iconRes!!),
                isChecked = item.isChecked,
                enabled = item.isEnabled,
                contentDescription = stringResource(item.descriptionRes),
                onClick = { item.onClick.invoke(item) }
            )
        }
    }
}