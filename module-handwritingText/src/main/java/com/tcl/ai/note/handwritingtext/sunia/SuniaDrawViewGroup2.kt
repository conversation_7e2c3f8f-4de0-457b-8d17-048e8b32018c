package com.tcl.ai.note.handwritingtext.sunia

import android.annotation.SuppressLint
import android.content.Context
import android.widget.FrameLayout
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.handwritingtext.sunia.SuniaDrawViewController
import com.tcl.ai.note.handwritingtext.ui.draw.DemoTextView
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.utils.launchIO

@Deprecated("SuniaDrawViewController替换")
@SuppressLint("ViewConstructor")
class SuniaDrawViewGroup2(
    context: Context,
    val suniaDrawViewModel: SuniaDrawViewModel,
) : FrameLayout(context) {
    private val textView = DemoTextView(context)
    private val suniaDrawViewController = SuniaDrawViewController(context, suniaDrawViewModel)

    init {
        addView(textView)
        addView(suniaDrawViewController.holder)

        suniaDrawViewModel.viewModelScope.launchIO {
            suniaDrawViewModel.scaleInfoShared.collect { scaleInfo ->
                if (textView.isAttachedToWindow) {
                    textView.scrollTo(-scaleInfo.offsetX.toInt(), -scaleInfo.offsetY.toInt())
                    textView.apply {
//                    translationX = scaleInfo.offsetX
//                    translationY = scaleInfo.offsetY
                        pivotX = scaleInfo.scaleCenterX
                        pivotY = scaleInfo.scaleCenterY
                        scaleX = scaleInfo.scale
                        scaleY = scaleInfo.scale
                    }
                }
            }
        }
    }
}
