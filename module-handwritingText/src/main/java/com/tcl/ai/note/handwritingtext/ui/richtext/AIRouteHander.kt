package com.tcl.ai.note.handwritingtext.ui.richtext

import android.content.Context
import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.platform.LocalContext
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.controller.BottomRoute
import com.tcl.ai.note.controller.LoginErrorResult
import com.tcl.ai.note.controller.rememberLoginHandler
import com.tcl.ai.note.handwritingtext.database.entity.Note
import com.tcl.ai.note.handwritingtext.state.RichTextState
import com.tcl.ai.note.handwritingtext.vm.RichTextViewModel
import com.tcl.ai.note.track.TclAnalytics
import com.tcl.ai.note.utils.*
import com.tcl.ai.note.widget.components.AIServiceDialog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * AI登录状态处理
  */
data class AIRouteState(
    val context: Context,
    val coroutineScope: CoroutineScope,
    val loginHandler: (action: () -> Unit) -> Unit,
    val viewModel: RichTextViewModel,
) {

    fun handleRoute(savedNote: Note?,route: BottomRoute) {
        if (viewModel.isOffline.value){
            Toast.makeText(context, R.string.no_connection, Toast.LENGTH_SHORT).show()
            return
        }
        when (route) {
            is BottomRoute.Summary -> {
                GlobalContext.applicationScope.launchIO {
                    // 上报登录状态
                    TclAnalytics.reportAiSummaryLoginState(AccountController.getLoginState())
                }
                toAIRoute(savedNote) {
                    viewModel.startNavigation()
                    context.startAISummary(savedNote?.noteId ?: 0)
                }
            }
            is BottomRoute.HelpWrite -> {
                GlobalContext.applicationScope.launchIO {
                    // 上报登录状态
                    TclAnalytics.reportAiWriteLoginState(AccountController.getLoginState())
                }
                //没有写文字时，也要跳转到帮写页面
                toAICheckLoginState {
                    viewModel.startNavigation()
                    context.startAIHelpWriting(savedNote?.noteId?:0)
                }
            }
            is BottomRoute.Polish -> {
                GlobalContext.applicationScope.launchIO {
                    // 上报登录状态
                    TclAnalytics.reportAiRewriteLoginState(AccountController.getLoginState())
                }
                toAIRoute(savedNote) {
                    viewModel.startNavigation()
                    context.startAIPolish(savedNote?.noteId ?: 0)
                }
            }
        }
    }

    private fun toAIRoute(savedNote: Note?, onRoute: () -> Unit) {
        // 在使用时动态获取字符串资源
        val tooLongTips = context.getString(R.string.tips_content_too_long)
        val tooShortTips = context.getString(R.string.tips_content_too_short)
        val noContentTips = context.getString(R.string.not_content_text_function_unavailable)
        if (savedNote!=null){
            savedNote.let { note ->
                Logger.d("toAIRoute","note.content.length:${note.content.length}")
                when (note.content.length) {
                    in 20..10000 -> {
                        toAICheckLoginState(onRoute)
                    }

                    0 -> {
                        //发现富文本输入完，马上点击AI功能，出现Note的内容为空的情况，是富文本输入完文字之后做了1秒的延时防抖功能
                        showToastAndClosePop(noContentTips)
                    }

                    in 0 until 20 -> {
                        showToastAndClosePop(tooShortTips)
                    }

                    else ->{
                        showToastAndClosePop(tooLongTips)
                    }
                }
            }
        }else{
            showToastAndClosePop(noContentTips)
        }
    }

    private fun toAICheckLoginState(
        toRoute: () -> Unit,
    ) = coroutineScope.launch {
        if (AccountController.getLoginState()) {
            viewModel.closeBottomAIPop()
            toRoute()
        } else {
            loginHandler {
                viewModel.closeBottomAIPop()
                toRoute()
            }
        }
    }

    private fun showToastAndClosePop(noContentTips: String) {
        Toast.makeText(context, noContentTips, Toast.LENGTH_SHORT).show()
        viewModel.closeBottomAIPop()
    }
}

/*
 * AIRouteState 的状态
 */
@Composable
fun rememberAIRouteState(
    viewModel: RichTextViewModel,
    richTextState: RichTextState
): AIRouteState {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val showAiServiceDialog = remember { mutableStateOf(false) }
    val loginHandler = rememberLoginHandler(onFailure = {
        if (it == LoginErrorResult.LunchError) {
            showAiServiceDialog.value = true
            Logger.d("AIRouteState","loginHandler onFailure")
        }
    })
    AIServiceDialog(
        isShow = showAiServiceDialog.value,
        title = context.getString(R.string.edit_bottom_menu_ai_assistant),
        onDismiss = { showAiServiceDialog.value = false },
        onGoToSettings = {
            showAiServiceDialog.value = false
        }
    )

    return remember(viewModel, loginHandler) {
        AIRouteState(
            context = context,
            coroutineScope = coroutineScope,
            loginHandler = loginHandler,
            viewModel = viewModel,
        )
    }
}