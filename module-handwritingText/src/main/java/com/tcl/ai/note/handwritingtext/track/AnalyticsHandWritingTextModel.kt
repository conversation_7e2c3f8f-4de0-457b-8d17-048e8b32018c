package com.tcl.ai.note.handwritingtext.track

import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.handwritingtext.bean.StrokeStyle
import com.tcl.ai.note.handwritingtext.database.entity.BgMode
import com.tcl.ai.note.handwritingtext.lowlatency.gl.FastRender
import com.tcl.ai.note.handwritingtext.state.RichTextState
import com.tcl.ai.note.handwritingtext.vm.DrawBoardViewModel
import com.tcl.ai.note.handwritingtext.vm.RichTextViewModel
import com.tcl.ai.note.handwritingtext.vm.SkinViewModel
import com.tcl.ai.note.track.AbsAnalyticsSubModel
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.lang.ref.WeakReference

/**
 * 分析手写界面使用情况
 *
 * 用于上报埋点
 */
object AnalyticsHandWritingTextModel: AbsAnalyticsSubModel() {
    private const val TAG = "AnalysisEditScreen"
    private var skinViewModelRef: WeakReference<SkinViewModel>? = null

    // 富文本数据状态
    private var _richTextStateFlow: MutableStateFlow<RichTextState?> = MutableStateFlow(null)
    val richTextStateFlow = _richTextStateFlow.asStateFlow()

    // 笔类型
    private var _strokeStyleStateFlow: MutableStateFlow<StrokeStyle?> = MutableStateFlow(null)
    val strokeStyleStateFlow = _strokeStyleStateFlow.asStateFlow()

    // 获取皮肤id
    fun getSkinName(): BgMode {
        return skinViewModelRef?.get()?.bgModeState?.value ?: BgMode.none
    }

    /**
     * 获取RichTextViewModel的内容，比如标题，正文，录音，图片等等
     */
    internal fun loadRichTextViewModel(richTextViewModel: RichTextViewModel) {
        richTextViewModel.state.collectWithScope(richTextViewModel.viewModelScope) { richTextState ->
            _richTextStateFlow.value = richTextState
        }.invokeOnCompletion {
            // 如果viewmodel被移除，需要移除数据
            _richTextStateFlow.value = null
        }
    }

    /**
     * 获取DrawBoardViewModel的笔画切换
     */
    internal fun loadDrawBoardViewModel(drawBoardViewModel: DrawBoardViewModel) {
        drawBoardViewModel.fastRenderIntentFlow.collectWithScope(drawBoardViewModel.viewModelScope) { changeStrokeStyle ->
            if (changeStrokeStyle is FastRender.Intent.ChangeStrokeStyle) {
                _strokeStyleStateFlow.value = changeStrokeStyle.strokeStyle
                 Logger.d(TAG, "changeStrokeStyle: $changeStrokeStyle")
            }
        }.invokeOnCompletion {
            // 如果viewmodel被移除，需要移除数据
            _strokeStyleStateFlow.value = null
            Logger.d(TAG, "changeStrokeStyle--invokeOnCompletion: $it")
        }
    }

    /**
     * 获取DrawBoardViewModel的笔画切换
     */
    internal fun loadSkinViewModel(skinViewModel: SkinViewModel) {
        this.skinViewModelRef = WeakReference(skinViewModel)
    }
}