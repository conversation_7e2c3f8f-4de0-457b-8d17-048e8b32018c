package com.tcl.ai.note.handwritingtext.demo

import android.os.Bundle
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.tcl.ai.note.handwritingtext.R
import com.tcl.ai.note.handwritingtext.databinding.ActivityRichTextDemoBinding

/**
 * NestedScrollView + EditText
 */
class RichTextDemoActivity : AppCompatActivity() {

    private val TAG = "RichTextDemoActivity"
    lateinit var binding: ActivityRichTextDemoBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 隐藏 ActionBar
//        supportActionBar?.hide()
//        enableEdgeToEdge()
        binding = ActivityRichTextDemoBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        // 设置边缘到边缘显示
//        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
//            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
//            v.setPadding(systemBars.left, 0, systemBars.right, systemBars.bottom)
//            insets
//        }

    }
}