package com.tcl.ai.note.handwritingtext.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.ui.menu.TableMenuAiGroup
import com.tcl.ai.note.handwritingtext.ui.menu.TableMenuUndoRedoGroup
import com.tcl.ai.note.handwritingtext.ui.menu.TabletMenuColorGroup
import com.tcl.ai.note.handwritingtext.ui.menu.TabletMenuToolGroup
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.TabletNavigationBar
import com.tcl.ai.note.handwritingtext.vm.MenuBarViewModel
import com.tcl.ai.note.theme.getRichTextMenuDimens
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.widget.DelayedBackgroundIconButton
import com.tcl.ai.note.widget.HorizontalLine

@Composable
fun TableEditScreenTopBar(
    navController: NavController,
    modifier: Modifier = Modifier
) {
    Column (
        modifier =modifier.fillMaxWidth().statusBarsPadding()
    ) {
        TabletNavigationBar(navController)
        TopMenuBar(modifier = modifier)
        HorizontalLine()
    }
}


@Composable
internal fun TopMenuBar(
    modifier: Modifier = Modifier,
    menuBarViewModel: MenuBarViewModel = hiltViewModel()){

    val dimens = getRichTextMenuDimens()
    val menuBarState by menuBarViewModel.menuBarState.collectAsState()
    var popupContent: (@Composable () -> Unit)? by remember { mutableStateOf(null) }
    val isKeyboardActive = menuBarState.isKeyboardActive

    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(dimens.topBarHeight),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        TabletMenuToolGroup(
            menuBarState= menuBarState,
            setPopupComposable= { newPopupComposable ->
                popupContent =newPopupComposable
            }
        )

       // TableMenuUndoRedoGroup(menuBarState= menuBarState)

       /* TabletMenuColorGroup(
            menuBarState= menuBarState,
            setPopupComposable= { newPopupComposable ->
                popupContent =newPopupComposable
            },
            updateColor = {colorIdx,color ->
                menuBarViewModel.updateColor(colorIdx,color)
            },
            updateSelColorIdx = {colorIdx ->
                menuBarViewModel.updateSelColorIdx(colorIdx)
            }
        )*/
        //TableMenuAiGroup(menuBarState= menuBarState)
    }

    popupContent?.let { it() }
}