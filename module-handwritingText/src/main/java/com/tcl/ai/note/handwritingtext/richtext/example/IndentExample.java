package com.tcl.ai.note.handwritingtext.richtext.example;

import android.content.Context;
import android.text.SpannableStringBuilder;
import android.text.Spanned;

import com.tcl.ai.note.handwritingtext.richtext.inner.Html;
import com.tcl.ai.note.handwritingtext.richtext.spans.AreLeadingMarginSpan;

/**
 * ARE_IndentRight的Span与HTML属性转换示例
 * 
 * 这个类展示了如何使用AreLeadingMarginSpan进行缩进，
 * 以及如何在Span和HTML之间进行转换。
 */
public class IndentExample {

    /**
     * 创建带有缩进的Spanned文本
     * 
     * @param context Android上下文
     * @param text 要缩进的文本
     * @param indentLevel 缩进级别（1=2em, 2=4em, 3=6em, ...）
     * @return 带有缩进的Spanned对象
     */
    public static Spanned createIndentedText(Context context, String text, int indentLevel) {
        SpannableStringBuilder builder = new SpannableStringBuilder(text);
        
        if (indentLevel > 0) {
            AreLeadingMarginSpan indentSpan = new AreLeadingMarginSpan(context);
            indentSpan.setLevel(indentLevel);
            
            // 应用缩进到整个文本
            builder.setSpan(indentSpan, 0, builder.length(), Spanned.SPAN_PARAGRAPH);
        }
        
        return builder;
    }

    /**
     * 将带有缩进的Spanned转换为HTML
     * 
     * @param spanned 包含缩进的Spanned对象
     * @return HTML字符串
     */
    public static String convertSpannedToHtml(Spanned spanned) {
        return Html.toHtml(spanned, Html.TO_HTML_PARAGRAPH_LINES_INDIVIDUAL);
    }

    /**
     * 将包含缩进样式的HTML转换为Spanned
     * 
     * @param html 包含margin-left样式的HTML字符串
     * @return Spanned对象
     */
    public static Spanned convertHtmlToSpanned(String html) {
        return Html.fromHtml(html, Html.FROM_HTML_MODE_COMPACT);
    }

    /**
     * 示例：创建多级缩进的文本
     * 
     * @param context Android上下文
     * @return 包含多级缩进的HTML字符串
     */
    public static String createMultiLevelIndentExample(Context context) {
        StringBuilder htmlBuilder = new StringBuilder();
        
        // 创建不同级别的缩进文本
        String[] texts = {
            "这是正常文本（无缩进）",
            "这是一级缩进文本",
            "这是二级缩进文本", 
            "这是三级缩进文本"
        };
        
        for (int i = 0; i < texts.length; i++) {
            Spanned indentedText = createIndentedText(context, texts[i], i);
            String html = convertSpannedToHtml(indentedText);
            htmlBuilder.append(html);
        }
        
        return htmlBuilder.toString();
    }

    /**
     * 示例：解析HTML并提取缩进信息
     * 
     * @param html 包含缩进样式的HTML
     * @return 缩进信息的描述
     */
    public static String analyzeIndentInHtml(String html) {
        Spanned spanned = convertHtmlToSpanned(html);
        StringBuilder result = new StringBuilder();
        
        // 查找所有的AreLeadingMarginSpan
        AreLeadingMarginSpan[] indentSpans = spanned.getSpans(0, spanned.length(), AreLeadingMarginSpan.class);
        
        if (indentSpans.length == 0) {
            result.append("没有发现缩进");
        } else {
            result.append("发现 ").append(indentSpans.length).append(" 个缩进区域:\n");
            
            for (int i = 0; i < indentSpans.length; i++) {
                AreLeadingMarginSpan span = indentSpans[i];
                int start = spanned.getSpanStart(span);
                int end = spanned.getSpanEnd(span);
                String text = spanned.subSequence(start, end).toString();
                
                result.append("  区域 ").append(i + 1).append(": ")
                      .append("级别=").append(span.getLevel())
                      .append(", 文本=\"").append(text.trim()).append("\"\n");
            }
        }
        
        return result.toString();
    }

    /**
     * 示例HTML字符串，包含不同的缩进级别
     */
    public static final String SAMPLE_HTML = 
        "<p>正常段落，无缩进</p>" +
        "<p style=\"margin-left:2em;\">一级缩进段落</p>" +
        "<p style=\"margin-left:4em;\">二级缩进段落</p>" +
        "<p style=\"margin-left:6em;\">三级缩进段落</p>" +
        "<p style=\"margin-left:80px;\">使用像素单位的缩进（等同于二级）</p>";

    /**
     * 测试方法：演示完整的转换流程
     * 
     * @param context Android上下文
     */
    public static void demonstrateIndentConversion(Context context) {
        System.out.println("=== ARE_IndentRight Span与HTML转换示例 ===\n");
        
        // 1. 创建带缩进的Spanned
        System.out.println("1. 创建带缩进的Spanned文本:");
        Spanned indentedSpanned = createIndentedText(context, "这是一个二级缩进的示例文本", 2);
        System.out.println("   文本: " + indentedSpanned.toString());
        
        // 2. 转换为HTML
        System.out.println("\n2. 转换为HTML:");
        String html = convertSpannedToHtml(indentedSpanned);
        System.out.println("   HTML: " + html);
        
        // 3. 从HTML转换回Spanned
        System.out.println("\n3. 从HTML转换回Spanned:");
        Spanned convertedSpanned = convertHtmlToSpanned(html);
        System.out.println("   文本: " + convertedSpanned.toString());
        
        // 4. 验证缩进级别
        AreLeadingMarginSpan[] spans = convertedSpanned.getSpans(0, convertedSpanned.length(), AreLeadingMarginSpan.class);
        if (spans.length > 0) {
            System.out.println("   缩进级别: " + spans[0].getLevel());
        }
        
        // 5. 分析示例HTML
        System.out.println("\n4. 分析示例HTML:");
        System.out.println("   HTML内容:\n" + SAMPLE_HTML);
        System.out.println("\n   分析结果:");
        System.out.println(analyzeIndentInHtml(SAMPLE_HTML));
    }
}
