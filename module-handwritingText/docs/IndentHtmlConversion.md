# ARE_IndentRight Span与HTML属性转换实现

## 概述

本文档描述了如何实现ARE_IndentRight的Span与HTML属性之间的双向转换功能。该功能允许将Android中的文本缩进样式转换为HTML的CSS样式，以及从HTML解析回Android的Span。

## 实现的功能

### 1. Span转HTML
- 将`AreLeadingMarginSpan`转换为HTML的`margin-left`CSS样式
- 支持多级缩进（每级对应2em的CSS缩进）
- 自动生成符合标准的HTML代码

### 2. HTML转Span
- 解析HTML中的`margin-left`CSS样式
- 支持`em`和`px`两种CSS单位
- 自动创建对应的`AreLeadingMarginSpan`

## 核心修改

### 1. AreLeadingMarginSpan类修改

```java
// 实现ARE_Span接口
public class AreLeadingMarginSpan implements LeadingMarginSpan, ARE_Span {
    
    @Override
    public String getHtml() {
        if (mLevel <= 0) {
            return "";
        }
        // 使用CSS的margin-left来实现左缩进效果
        // 每个level对应2em的缩进（大约两个汉字的宽度）
        return String.format(" style=\"margin-left: %dem;\"", mLevel * 2);
    }
}
```

### 2. Html类修改

#### 添加导入
```java
import com.tcl.ai.note.handwritingtext.richtext.spans.AreLeadingMarginSpan;
```

#### 修改getTextStyles方法
```java
private static String getTextStyles(Spanned text, int start, int end,
                                    boolean forceNoVerticalMargin, boolean includeTextAlign) {
    String textAlign = null;
    String marginLeft = null;

    // ... 处理对齐的代码 ...

    // 处理缩进
    final AreLeadingMarginSpan[] leadingMarginSpans = text.getSpans(start, end, AreLeadingMarginSpan.class);
    for (int i = leadingMarginSpans.length - 1; i >= 0; i--) {
        AreLeadingMarginSpan s = leadingMarginSpans[i];
        if (s.getLevel() > 0) {
            marginLeft = String.format("margin-left:%dem;", s.getLevel() * 2);
            break; // 只取最后一个有效的缩进
        }
    }

    // 组合样式
    StringBuilder styleBuilder = new StringBuilder();
    if (textAlign != null) {
        styleBuilder.append(textAlign);
    }
    if (marginLeft != null) {
        styleBuilder.append(marginLeft);
    }

    if (styleBuilder.length() == 0) {
        return "";
    }
    return " style=\"" + styleBuilder.toString() + "\"";
}
```

#### 添加HTML解析支持
```java
// 添加margin-left的正则表达式
private static Pattern sMarginLeftPattern;

private static Pattern getMarginLeftPattern() {
    if (sMarginLeftPattern == null) {
        sMarginLeftPattern = Pattern.compile("(?:\\s+|\\A)margin-left\\s*:\\s*(\\S*)\\b");
    }
    return sMarginLeftPattern;
}

// 在startCssStyle方法中添加处理逻辑
private void startCssStyle(Editable text, Attributes attributes) {
    String style = attributes.getValue("", "style");
    if (style != null) {
        // ... 其他样式处理 ...

        // 处理margin-left缩进
        Matcher m = getMarginLeftPattern().matcher(style);
        if (m.find()) {
            String marginValue = m.group(1);
            int level = parseMarginLeftLevel(marginValue);
            if (level > 0) {
                start(text, new MarginLeft(level));
            }
        }
    }
}

// 在endCssStyle方法中添加处理逻辑
private static void endCssStyle(Editable text) {
    // ... 其他样式处理 ...

    // 处理缩进
    MarginLeft marginLeft = getLast(text, MarginLeft.class);
    if (marginLeft != null) {
        AreLeadingMarginSpan leadingMarginSpan = new AreLeadingMarginSpan(Html.sContext);
        leadingMarginSpan.setLevel(marginLeft.mLevel);
        setSpanFromMark(text, marginLeft, leadingMarginSpan);
    }
}

// 添加内部类
private static class MarginLeft {
    private int mLevel;

    public MarginLeft(int level) {
        mLevel = level;
    }
}

// 添加解析方法
private static int parseMarginLeftLevel(String marginValue) {
    try {
        if (marginValue.endsWith("em")) {
            String valueStr = marginValue.substring(0, marginValue.length() - 2);
            float emValue = Float.parseFloat(valueStr);
            // 每2em对应一个缩进级别
            return (int) (emValue / 2);
        } else if (marginValue.endsWith("px")) {
            String valueStr = marginValue.substring(0, marginValue.length() - 2);
            int pxValue = Integer.parseInt(valueStr);
            // 假设每40px对应一个缩进级别（大约两个汉字的宽度）
            return pxValue / 40;
        }
    } catch (NumberFormatException e) {
        // 解析失败，返回0
    }
    return 0;
}
```

## 使用示例

### 1. 创建带缩进的文本
```java
Context context = getContext();
SpannableStringBuilder builder = new SpannableStringBuilder("这是缩进文本");

AreLeadingMarginSpan indentSpan = new AreLeadingMarginSpan(context);
indentSpan.setLevel(2); // 设置为2级缩进

builder.setSpan(indentSpan, 0, builder.length(), Spanned.SPAN_PARAGRAPH);
```

### 2. 转换为HTML
```java
String html = Html.toHtml(builder, Html.TO_HTML_PARAGRAPH_LINES_INDIVIDUAL);
// 输出: <p style="margin-left:4em;">这是缩进文本</p>
```

### 3. 从HTML解析
```java
String html = "<p style=\"margin-left:4em;\">这是缩进文本</p>";
Spanned spanned = Html.fromHtml(html, Html.FROM_HTML_MODE_COMPACT);

// 获取缩进信息
AreLeadingMarginSpan[] spans = spanned.getSpans(0, spanned.length(), AreLeadingMarginSpan.class);
if (spans.length > 0) {
    int level = spans[0].getLevel(); // level = 2
}
```

## 支持的CSS单位

### em单位
- `2em` → 缩进级别1
- `4em` → 缩进级别2
- `6em` → 缩进级别3
- 以此类推...

### px单位
- `40px` → 缩进级别1
- `80px` → 缩进级别2
- `120px` → 缩进级别3
- 以此类推...

## 测试

项目包含完整的单元测试，位于：
- `module-handwritingText/src/test/java/com/tcl/ai/note/handwritingtext/richtext/IndentHtmlTest.java`

测试覆盖：
- Span转HTML的正确性
- HTML转Span的正确性
- 不同CSS单位的支持
- 往返转换的一致性

## 示例代码

完整的示例代码位于：
- `module-handwritingText/src/main/java/com/tcl/ai/note/handwritingtext/richtext/example/IndentExample.java`

## 注意事项

1. **Context依赖**: `AreLeadingMarginSpan`需要Android Context来计算缩进宽度
2. **CSS单位**: 目前支持`em`和`px`两种单位，其他单位会被忽略
3. **级别限制**: 缩进级别应该为正整数，0或负数表示无缩进
4. **兼容性**: 该实现与现有的富文本编辑器功能完全兼容

## 总结

通过以上实现，ARE_IndentRight的Span现在可以：
1. 正确转换为HTML的CSS样式
2. 从HTML的CSS样式正确解析回Span
3. 支持多级缩进
4. 保持往返转换的一致性

这为富文本编辑器提供了完整的缩进功能HTML导入导出支持。
